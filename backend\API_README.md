# Wildlife Anomaly Detection API

A FastAPI-based REST API for detecting wildlife anomalies in videos using YOLOv5 models.

## Features

- Asynchronous video processing
- Support for both Daylight and Thermal videos
- RESTful API with proper error handling
- File upload and download endpoints
- Background task processing
- Location-based alerts
- CORS support for frontend integration

## Installation

1. Install the additional API requirements:

```bash
pip install -r requirements-api.txt
```

2. Make sure you have the main requirements installed:

```bash
pip install -r requirements.txt
```

## Running the API

### Option 1: Using the new API (Recommended)

```bash
python api.py
```

or

```bash
uvicorn api:app --host 0.0.0.0 --port 8000 --reload
```

### Option 2: Using the original FastAPI app

```bash
fastapi dev main.py
```

The API will be available at `http://localhost:8000`

## API Documentation

Once running, visit:

- Interactive API docs: `http://localhost:8000/docs`
- Alternative docs: `http://localhost:8000/redoc`

## API Endpoints

### 1. Health Check

```http
GET /api/v1/health
```

Returns API health status.

### 2. Upload Video for Processing

```http
POST /api/v1/upload
```

**Form Data:**

- `video`: Video file (mp4, avi, mov, mkv, webm)
- `video_type`: "Daylight" or "Thermal" (default: "Daylight")
- `enable_alerts`: Boolean (default: false)
- `location_lat`: Float (default: 22.606803)
- `location_lon`: Float (default: 85.338173)

**Response:**

```json
{
  "task_id": "uuid-string",
  "status": "queued",
  "message": "Video uploaded successfully. Processing started."
}
```

### 3. Check Processing Status

```http
GET /api/v1/status/{task_id}
```

**Response:**

```json
{
  "task_id": "uuid-string",
  "status": "completed|processing|queued|failed",
  "original_video_url": "/api/v1/download/input/{task_id}",
  "processed_video_url": "/api/v1/download/output/{task_id}",
  "results": [...],
  "processing_time": 45.2,
  "error_message": null
}
```

### 4. Download Videos

```http
GET /api/v1/download/input/{task_id}   # Original video
GET /api/v1/download/output/{task_id}  # Processed video with detections
```

### 5. List All Tasks (Debug)

```http
GET /api/v1/tasks
```

### 6. Delete Task and Files

```http
DELETE /api/v1/tasks/{task_id}
```

## Example Usage

### Python Client

See `test_api_client.py` for a complete example.

```python
import requests

# Upload video
files = {'video': open('video.mp4', 'rb')}
data = {'video_type': 'Daylight', 'enable_alerts': False}
response = requests.post('http://localhost:8000/api/v1/upload', files=files, data=data)
task_id = response.json()['task_id']

# Check status
status = requests.get(f'http://localhost:8000/api/v1/status/{task_id}').json()

# Download processed video when ready
if status['status'] == 'completed':
    video_response = requests.get(f'http://localhost:8000/api/v1/download/output/{task_id}')
    with open('processed_video.webm', 'wb') as f:
        f.write(video_response.content)
```

### JavaScript/Frontend

```javascript
async function uploadVideo(videoFile, videoType = "Daylight") {
  const formData = new FormData();
  formData.append("video", videoFile);
  formData.append("video_type", videoType);
  formData.append("enable_alerts", false);

  const response = await fetch("http://localhost:8000/api/v1/upload", {
    method: "POST",
    body: formData,
  });

  return await response.json();
}

async function checkStatus(taskId) {
  const response = await fetch(`http://localhost:8000/api/v1/status/${taskId}`);
  return await response.json();
}

// Usage
const result = await uploadVideo(videoFile, "Daylight");
const taskId = result.task_id;

// Poll for completion
const pollStatus = setInterval(async () => {
  const status = await checkStatus(taskId);
  if (status.status === "completed") {
    clearInterval(pollStatus);
    // Download or display results
    console.log("Results:", status.results);
    // Video available at: status.processed_video_url
  }
}, 5000);
```

### cURL Examples

```bash
# Upload video
curl -X POST "http://localhost:8000/api/v1/upload" \
     -F "video=@test_video.mp4" \
     -F "video_type=Daylight" \
     -F "enable_alerts=false"

# Check status
curl "http://localhost:8000/api/v1/status/{task_id}"

# Download processed video
curl "http://localhost:8000/api/v1/download/output/{task_id}" -o processed_video.webm
```

## Response Format

### Detection Results

Each detection result contains:

```json
{
  "frame_number": 1,
  "detections": [
    {
      "class_id": 1,
      "class_name": "Elephant",
      "confidence": 0.95,
      "location": {
        "latitude": 22.61,
        "longitude": 85.342
      }
    }
  ]
}
```

### Supported Classes

**Daylight Model:**

- Person, Elephant, Zebra, Giraffe, Deer, Bison, Rhino, Boar, Leopard, Vehicle, Fire

**Thermal Model:**

- Person, Elephant, Deer, Rhino, Boar, Leopard, Vehicle, Fire

## Error Handling

The API returns appropriate HTTP status codes:

- `200`: Success
- `400`: Bad Request (invalid parameters)
- `404`: Not Found (task/file not found)
- `422`: Validation Error
- `500`: Internal Server Error

## Limitations

- Maximum file size: 100MB
- Supported formats: mp4, avi, mov, mkv, webm
- Processing is CPU-based (can be slow for large videos)
- Task storage is in-memory (use Redis for production)

## Production Considerations

For production deployment:

1. Use a proper database for task storage
2. Implement user authentication
3. Add rate limiting
4. Use a queue system (Celery/RQ) for background tasks
5. Configure CORS properly
6. Add file cleanup jobs
7. Monitor disk space
8. Add logging and metrics
9. Use HTTPS
10. Scale with multiple workers

## Testing

Run the test client:

```bash
python test_api_client.py
```

Make sure to update the `video_path` in the test script to point to your test video file.
