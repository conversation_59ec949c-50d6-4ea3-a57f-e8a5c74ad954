[project]
name = "wildlife-anomaly-detection-api"
version = "1.0.0"
description = "Comprehensive REST API for wildlife anomaly detection using YOLOv5"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi[standard]>=0.116.1",
    "uvicorn[standard]>=0.30.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-multipart>=0.0.6",
    "pillow>=10.3.0",
    "numpy>=1.23.5",
    "opencv-python>=*********",
    "torch>=1.8.0",
    "torchvision>=0.9.0",
    "ultralytics>=8.2.34",
    "matplotlib>=3.3",
    "pandas>=1.1.4",
    "scipy>=1.4.1",
    "pyyaml>=5.3.1",
    "requests>=2.32.2",
    "tqdm>=4.66.3",
    "psutil>=7.0.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-dotenv>=1.0.0",
    "structlog>=23.0.0",
    "prometheus-client>=0.20.0",
    "slowapi>=0.1.9",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "httpx>=0.24.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=api --cov-report=term-missing"
