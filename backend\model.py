import torch
import cv2
import pathlib
import functools
import os

temp = pathlib.PosixPath
pathlib.PosixPath = pathlib.WindowsPath

# Monkey patch torch.load to handle PyTorch 2.6+ weights_only default change
original_torch_load = torch.load


@functools.wraps(torch.load)
def patched_torch_load(*args, **kwargs):
    # If weights_only is not specified, set it to False for YOLOv5 compatibility
    if "weights_only" not in kwargs:
        kwargs["weights_only"] = False
    return original_torch_load(*args, **kwargs)


# Apply the patch
torch.load = patched_torch_load

# Load the YOLOv5 models
day_model = torch.hub.load(
    "yolov5",
    "custom",
    r"weights/day-final.pt",
    source="local",
    force_reload=True,
    trust_repo=True,
    device="cpu",
)
thermal_model = torch.hub.load(
    "yolov5",
    "custom",
    path=r"weights/night-final-aug.pt",
    source="local",
    force_reload=True,
    device="cpu",
)

# Restore original torch.load after loading models
torch.load = original_torch_load

day_class_to_animal = {
    0: "Person",
    1: "Elephant",
    2: "Zebra",
    3: "Giraffe",
    4: "Deer",
    5: "Bison",
    6: "Rhino",
    7: "Boar",
    8: "Leopard",
    9: "Vehicle",
    10: "Fire",
}
thermal_class_to_animal = {
    0: "Person",
    1: "Elephant",
    2: "Deer",
    3: "Rhino",
    4: "Boar",
    5: "Leopard",
    6: "Vehicle",
    7: "Fire",
}


def plotBbox(results, frame, class_dict):
    for box in results.xyxy[0]:
        xA, yA, xB, yB, confidence, class_id = box
        class_id = int(class_id)
        class_name = class_dict.get(int(class_id), "Unknown")
        # Define a unique color for each class
        color = get_color(class_id)
        xA = int(xA)
        xB = int(xB)
        yA = int(yA)
        yB = int(yB)
        # Draw the bounding box with the class-specific color
        cv2.rectangle(frame, (xA, yA), (xB, yB), color, 2)

        # Add text label with class name and confidence
        label = f"{class_name}: {confidence:.2f}"
        y = yA - 15 if yA - 15 > 15 else yA + 15
        cv2.putText(frame, label, (xA, y), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)

    return frame


def get_color(class_id):
    # Define a list of colors for different classes
    colors = [
        (255, 99, 71),
        (124, 252, 0),
        (255, 215, 0),
        (255, 255, 0),
        (0, 255, 255),
        (255, 0, 255),
        (255, 218, 185),
        (138, 43, 226),
        (255, 20, 147),
        (176, 196, 222),
        (0, 250, 154),
    ]

    return colors[class_id]


def predictVideo(temp_video_path, video_class):
    """
    Process video for wildlife detection

    Args:
        temp_video_path: Relative path to the video file
        video_class: "Daylight" or "Thermal"

    Returns:
        tuple: (results_list, output_path)
    """
    try:
        # Open video file
        cap = cv2.VideoCapture(f"static/{temp_video_path}")

        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {temp_video_path}")

        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        if fps <= 0 or width <= 0 or height <= 0:
            raise ValueError("Invalid video properties")

        print(f"Video info: {width}x{height}, {fps} FPS, {total_frames} frames")

        # Create output directory if it doesn't exist
        os.makedirs("static/out", exist_ok=True)

        output_path = "/out/output_video.webm"
        # Use different fourcc code that works with cv2
        fourcc = cv2.VideoWriter_fourcc(*"VP80")
        out = cv2.VideoWriter(f"static/{output_path}", fourcc, fps, (width, height))

        if not out.isOpened():
            # Fallback to MP4 if WebM doesn't work
            output_path = "/out/output_video.mp4"
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            out = cv2.VideoWriter(f"static/{output_path}", fourcc, fps, (width, height))

        result = []
        frame_count = 0
        print("Prediction start")

        # Select model based on video class
        model = day_model if video_class == "Daylight" else thermal_model
        class_dict = (
            day_class_to_animal
            if video_class == "Daylight"
            else thermal_class_to_animal
        )

        while cap.isOpened():
            success, frame = cap.read()

            if not success:
                print("End of video or failed to read frame")
                break

            frame_count += 1

            # Resize frame to maintain aspect ratio
            frame = cv2.resize(frame, (width, height))

            # Run model inference
            results = model(frame)
            print(f"Frame {frame_count}: Processing complete")

            # Visualize results on frame
            annotated_frame = plotBbox(
                results=results, frame=frame, class_dict=class_dict
            )
            out.write(annotated_frame)

            # Process detections if any
            if len(results.xyxy[0]) > 0:
                try:
                    classes = results.pandas().xyxy[0]["class"]
                    confidences = results.pandas().xyxy[0]["confidence"]
                    print(
                        f"Frame {frame_count} detections - Classes: {classes.values}, Confidences: {confidences.values}"
                    )

                    frame_detections = {"frame_number": frame_count, "detections": []}

                    for class_id, confidence in zip(classes, confidences):
                        obj = {
                            "class_id": int(class_id),
                            "class_name": class_dict.get(int(class_id), "Unknown"),
                            "confidence": float(confidence),
                        }
                        frame_detections["detections"].append(obj)

                    result.append(frame_detections)
                except Exception as e:
                    print(f"Error processing detections for frame {frame_count}: {e}")

        cap.release()
        out.release()
        cv2.destroyAllWindows()

        print(
            f"Processing complete. Total frames: {frame_count}, Detections in {len(result)} frames"
        )
        return result, output_path

    except Exception as e:
        print(f"Error in predictVideo: {e}")
        # Clean up resources
        try:
            cap.release()
            out.release()
            cv2.destroyAllWindows()
        except:
            pass
        raise e
