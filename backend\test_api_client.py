#!/usr/bin/env python3
"""
Simple API client example for testing the Wildlife Anomaly Detection API
"""

import requests
import time
import json
from pathlib import Path

API_BASE_URL = "http://localhost:8000"


def upload_video(
    video_path: str, video_type: str = "Daylight", enable_alerts: bool = False
):
    """Upload a video to the API for processing"""

    url = f"{API_BASE_URL}/api/v1/upload"

    # Check if file exists
    if not Path(video_path).exists():
        print(f"Error: Video file {video_path} not found")
        return None

    # Prepare the files and data
    with open(video_path, "rb") as video_file:
        files = {"video": video_file}
        data = {"video_type": video_type, "enable_alerts": enable_alerts}

        print(f"Uploading {video_path}...")
        response = requests.post(url, files=files, data=data)

    if response.status_code == 200:
        result = response.json()
        print(f"Upload successful! Task ID: {result['task_id']}")
        return result["task_id"]
    else:
        print(f"Upload failed: {response.status_code} - {response.text}")
        return None


def check_status(task_id: str):
    """Check the status of a processing task"""

    url = f"{API_BASE_URL}/api/v1/status/{task_id}"
    response = requests.get(url)

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Status check failed: {response.status_code} - {response.text}")
        return None


def wait_for_completion(task_id: str, timeout: int = 300):
    """Wait for video processing to complete"""

    print(f"Waiting for task {task_id} to complete...")
    start_time = time.time()

    while time.time() - start_time < timeout:
        status = check_status(task_id)
        if not status:
            return None

        print(f"Status: {status['status']} - {status.get('message', '')}")

        if status["status"] == "completed":
            return status
        elif status["status"] == "failed":
            print(f"Processing failed: {status.get('error_message', 'Unknown error')}")
            return status

        time.sleep(5)  # Check every 5 seconds

    print("Timeout waiting for completion")
    return None


def download_video(task_id: str, output_path: str, video_type: str = "output"):
    """Download processed video"""

    url = f"{API_BASE_URL}/api/v1/download/{video_type}/{task_id}"
    response = requests.get(url)

    if response.status_code == 200:
        with open(output_path, "wb") as f:
            f.write(response.content)
        print(f"Video downloaded to {output_path}")
        return True
    else:
        print(f"Download failed: {response.status_code} - {response.text}")
        return False


def main():
    """Example usage of the API"""

    # Test video file path - replace with your actual video file
    video_path = "test_video.mp4"  # Update this path

    print("=== Wildlife Anomaly Detection API Test ===")

    # Check API health
    try:
        health_response = requests.get(f"{API_BASE_URL}/api/v1/health")
        if health_response.status_code == 200:
            print("✓ API is healthy")
        else:
            print("✗ API health check failed")
            return
    except requests.exceptions.ConnectionError:
        print(
            "✗ Cannot connect to API. Make sure it's running on http://localhost:8000"
        )
        return

    # Upload video
    task_id = upload_video(video_path, video_type="Daylight", enable_alerts=False)
    if not task_id:
        return

    # Wait for processing to complete
    final_status = wait_for_completion(task_id)
    if not final_status:
        return

    if final_status["status"] == "completed":
        print("\n=== Processing Results ===")
        print(f"Processing time: {final_status.get('processing_time', 'N/A')} seconds")

        # Print detection results
        results = final_status.get("results", [])
        total_detections = sum(len(frame["detections"]) for frame in results)
        print(f"Total detections: {total_detections} across {len(results)} frames")

        # Print summary of detected classes
        class_counts = {}
        for frame in results:
            for detection in frame["detections"]:
                class_name = detection["class_name"]
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

        if class_counts:
            print("Detected classes:")
            for class_name, count in class_counts.items():
                print(f"  {class_name}: {count} instances")

        # Download processed video
        output_filename = f"processed_{task_id}.webm"
        if download_video(task_id, output_filename):
            print(f"Processed video saved as: {output_filename}")

        # Optionally download original video
        original_filename = f"original_{task_id}.mp4"
        if download_video(task_id, original_filename, "input"):
            print(f"Original video saved as: {original_filename}")

        print("\n=== Test completed successfully! ===")
    else:
        print("Processing failed!")


if __name__ == "__main__":
    main()
