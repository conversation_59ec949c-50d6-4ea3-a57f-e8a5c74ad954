"""
Configuration management for Wildlife Anomaly Detection API
"""
import os
from pathlib import Path
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # API Configuration
    app_name: str = "Wildlife Anomaly Detection API"
    app_version: str = "1.0.0"
    app_description: str = "Comprehensive REST API for wildlife anomaly detection using YOLOv5"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # Security Configuration
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    allowed_hosts: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # CORS Configuration
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    cors_methods: List[str] = Field(default=["*"], env="CORS_METHODS")
    cors_headers: List[str] = Field(default=["*"], env="CORS_HEADERS")
    
    # Model Configuration
    model_dir: str = Field(default="weights", env="MODEL_DIR")
    day_model_path: str = Field(default="weights/day-final.pt", env="DAY_MODEL_PATH")
    thermal_model_path: str = Field(default="weights/night-final-aug.pt", env="THERMAL_MODEL_PATH")
    yolov5_repo_path: str = Field(default="../backend/yolov5", env="YOLOV5_REPO_PATH")
    model_device: str = Field(default="cpu", env="MODEL_DEVICE")  # "cpu" or "cuda"
    model_warmup: bool = Field(default=True, env="MODEL_WARMUP")
    
    # File Upload Configuration
    max_file_size: int = Field(default=10 * 1024 * 1024, env="MAX_FILE_SIZE")  # 10MB
    allowed_image_extensions: List[str] = Field(
        default=[".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"],
        env="ALLOWED_IMAGE_EXTENSIONS"
    )
    upload_dir: str = Field(default="static/uploads", env="UPLOAD_DIR")
    output_dir: str = Field(default="static/outputs", env="OUTPUT_DIR")
    temp_dir: str = Field(default="static/temp", env="TEMP_DIR")
    
    # Processing Configuration
    max_batch_size: int = Field(default=10, env="MAX_BATCH_SIZE")
    confidence_threshold: float = Field(default=0.25, env="CONFIDENCE_THRESHOLD")
    iou_threshold: float = Field(default=0.45, env="IOU_THRESHOLD")
    max_detections: int = Field(default=1000, env="MAX_DETECTIONS")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")  # "json" or "text"
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # Monitoring Configuration
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=8001, env="METRICS_PORT")
    
    # Rate Limiting Configuration
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Alert Configuration (for future integration)
    alerts_enabled: bool = Field(default=False, env="ALERTS_ENABLED")
    default_latitude: float = Field(default=22.606803, env="DEFAULT_LATITUDE")
    default_longitude: float = Field(default=85.338173, env="DEFAULT_LONGITUDE")
    max_distance_km: int = Field(default=20, env="MAX_DISTANCE_KM")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist"""
        directories = [
            self.upload_dir,
            self.output_dir,
            self.temp_dir,
            self.model_dir,
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @property
    def model_paths_exist(self) -> bool:
        """Check if model files exist"""
        day_path = Path(self.day_model_path)
        thermal_path = Path(self.thermal_model_path)
        return day_path.exists() and thermal_path.exists()
    
    @property
    def yolov5_repo_exists(self) -> bool:
        """Check if YOLOv5 repository exists"""
        return Path(self.yolov5_repo_path).exists()


# Global settings instance
settings = Settings()


# Class mappings for different model types
DAY_CLASS_MAPPING = {
    0: "Person",
    1: "Elephant", 
    2: "Zebra",
    3: "Giraffe",
    4: "Deer",
    5: "Bison",
    6: "Rhino",
    7: "Boar",
    8: "Leopard",
    9: "Vehicle",
    10: "Fire",
}

THERMAL_CLASS_MAPPING = {
    0: "Person",
    1: "Elephant",
    2: "Deer", 
    3: "Rhino",
    4: "Boar",
    5: "Leopard",
    6: "Vehicle",
    7: "Fire",
}

# Anomaly classes that trigger alerts
ANOMALY_CLASSES = ["Person", "Vehicle", "Fire"]

# Color mapping for visualization
CLASS_COLORS = {
    0: (255, 99, 71),   # Person - Tomato
    1: (124, 252, 0),   # Elephant - Lawn Green
    2: (255, 215, 0),   # Zebra - Gold
    3: (255, 255, 0),   # Giraffe - Yellow
    4: (0, 255, 255),   # Deer - Cyan
    5: (255, 0, 255),   # Bison - Magenta
    6: (255, 218, 185), # Rhino - Peach Puff
    7: (138, 43, 226),  # Boar - Blue Violet
    8: (255, 20, 147),  # Leopard - Deep Pink
    9: (176, 196, 222), # Vehicle - Light Steel Blue
    10: (0, 250, 154),  # Fire - Medium Spring Green
}
