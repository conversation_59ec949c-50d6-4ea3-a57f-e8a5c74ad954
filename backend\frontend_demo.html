<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Wildlife Anomaly Detection - Frontend Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    h1 {
      color: #2c3e50;
      text-align: center;
      margin-bottom: 30px;
    }

    .upload-area {
      border: 2px dashed #3498db;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      margin-bottom: 20px;
      transition: border-color 0.3s;
    }

    .upload-area:hover {
      border-color: #2980b9;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #34495e;
    }

    input[type="file"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    select,
    input[type="checkbox"] {
      margin-right: 10px;
    }

    button {
      background: #3498db;
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin: 5px;
    }

    button:hover {
      background: #2980b9;
    }

    button:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
    }

    .progress {
      margin-top: 20px;
    }

    .progress-bar {
      width: 100%;
      height: 20px;
      background: #ecf0f1;
      border-radius: 10px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: #3498db;
      width: 0%;
      transition: width 0.3s;
      text-align: center;
      line-height: 20px;
      color: white;
      font-size: 12px;
    }

    .results {
      margin-top: 20px;
      padding: 20px;
      background: #ecf0f1;
      border-radius: 5px;
    }

    .error {
      background: #e74c3c;
      color: white;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }

    .success {
      background: #27ae60;
      color: white;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }

    .video-container {
      margin: 20px 0;
    }

    video {
      width: 100%;
      max-width: 600px;
      border-radius: 5px;
    }

    .detection-item {
      background: white;
      padding: 10px;
      margin: 5px 0;
      border-left: 4px solid #3498db;
      border-radius: 3px;
    }

    .status {
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
      font-weight: bold;
    }

    .status.queued {
      background: #f39c12;
      color: white;
    }

    .status.processing {
      background: #3498db;
      color: white;
    }

    .status.completed {
      background: #27ae60;
      color: white;
    }

    .status.failed {
      background: #e74c3c;
      color: white;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>🦁 Wildlife Anomaly Detection API</h1>

    <div class="upload-area">
      <form id="uploadForm">
        <div class="form-group">
          <label for="videoFile">Select Video File:</label>
          <input type="file" id="videoFile" name="video" accept="video/*" required>
        </div>

        <div class="form-group">
          <label for="videoType">Video Type:</label>
          <select id="videoType" name="video_type">
            <option value="Daylight">Daylight</option>
            <option value="Thermal">Thermal</option>
          </select>
        </div>

        <div class="form-group">
          <label>
            <input type="checkbox" id="enableAlerts" name="enable_alerts">
            Enable Alerts
          </label>
        </div>

        <button type="submit" id="uploadBtn">Upload & Process Video</button>
      </form>
    </div>

    <div id="progressSection" style="display: none;">
      <div class="progress">
        <div class="progress-bar">
          <div class="progress-fill" id="progressFill">0%</div>
        </div>
      </div>
      <div id="statusMessage" class="status">Uploading...</div>
    </div>

    <div id="results" style="display: none;">
      <h3>Processing Results</h3>
      <div id="resultsContent"></div>
    </div>
  </div>

  <script>
    const API_BASE = 'http://localhost:8000';
    let currentTaskId = null;
    let pollInterval = null;

    document.getElementById('uploadForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      await uploadVideo();
    });

    async function uploadVideo() {
      const form = document.getElementById('uploadForm');
      const formData = new FormData(form);
      const uploadBtn = document.getElementById('uploadBtn');
      const progressSection = document.getElementById('progressSection');
      const results = document.getElementById('results');

      // Disable form and show progress
      uploadBtn.disabled = true;
      progressSection.style.display = 'block';
      results.style.display = 'none';

      try {
        // Upload video
        updateStatus('Uploading video...', 'queued');
        updateProgress(10);

        const response = await fetch(`${API_BASE}/api/v1/upload`, {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }

        const result = await response.json();
        currentTaskId = result.task_id;

        updateStatus('Upload successful! Processing started...', 'processing');
        updateProgress(20);

        // Start polling for status
        pollStatus();

      } catch (error) {
        showError(`Upload failed: ${error.message}`);
        uploadBtn.disabled = false;
        progressSection.style.display = 'none';
      }
    }

    async function pollStatus() {
      if (!currentTaskId) return;

      try {
        const response = await fetch(`${API_BASE}/api/v1/status/${currentTaskId}`);
        if (!response.ok) {
          throw new Error(`Status check failed: ${response.statusText}`);
        }

        const status = await response.json();

        switch (status.status) {
          case 'queued':
            updateStatus('Queued for processing...', 'queued');
            updateProgress(25);
            setTimeout(pollStatus, 2000);
            break;

          case 'processing':
            updateStatus('Processing video... This may take a while.', 'processing');
            updateProgress(50);
            setTimeout(pollStatus, 3000);
            break;

          case 'completed':
            updateStatus('Processing completed!', 'completed');
            updateProgress(100);
            await showResults(status);
            resetForm();
            break;

          case 'failed':
            updateStatus(`Processing failed: ${status.error_message || 'Unknown error'}`, 'failed');
            resetForm();
            break;

          default:
            updateStatus(`Unknown status: ${status.status}`, 'queued');
            setTimeout(pollStatus, 2000);
        }

      } catch (error) {
        showError(`Status check failed: ${error.message}`);
        resetForm();
      }
    }

    async function showResults(status) {
      const results = document.getElementById('results');
      const resultsContent = document.getElementById('resultsContent');

      let html = '';

      // Processing time
      if (status.processing_time) {
        html += `<div class="success">Processing completed in ${status.processing_time.toFixed(2)} seconds</div>`;
      }

      // Detection results
      const detections = status.results || [];
      const totalDetections = detections.reduce((sum, frame) => sum + frame.detections.length, 0);

      html += `<h4>Detection Summary</h4>`;
      html += `<p><strong>Total detections:</strong> ${totalDetections} across ${detections.length} frames</p>`;

      // Count by class
      const classCounts = {};
      detections.forEach(frame => {
        frame.detections.forEach(detection => {
          const className = detection.class_name;
          classCounts[className] = (classCounts[className] || 0) + 1;
        });
      });

      if (Object.keys(classCounts).length > 0) {
        html += `<h4>Detected Animals/Objects</h4>`;
        html += `<div>`;
        Object.entries(classCounts).forEach(([className, count]) => {
          html += `<div class="detection-item">
                        <strong>${className}:</strong> ${count} detection${count > 1 ? 's' : ''}
                    </div>`;
        });
        html += `</div>`;
      }

      // Video download links
      html += `<h4>Download Results</h4>`;
      html += `<div>`;

      if (status.processed_video_url) {
        html += `<button onclick="downloadVideo('${currentTaskId}', 'output')">Download Processed Video</button>`;
      }

      if (status.original_video_url) {
        html += `<button onclick="downloadVideo('${currentTaskId}', 'input')">Download Original Video</button>`;
      }

      html += `</div>`;

      // Show detailed frame-by-frame results if not too many
      if (detections.length > 0 && detections.length <= 20) {
        html += `<h4>Frame-by-Frame Detections</h4>`;
        html += `<div style="max-height: 300px; overflow-y: auto;">`;
        detections.forEach(frame => {
          html += `<div class="detection-item">
                        <strong>Frame ${frame.frame_number}:</strong>
                        ${frame.detections.map(d =>
            `${d.class_name} (${(d.confidence * 100).toFixed(1)}%)`
          ).join(', ')}
                    </div>`;
        });
        html += `</div>`;
      }

      resultsContent.innerHTML = html;
      results.style.display = 'block';
    }

    function downloadVideo(taskId, type) {
      const url = `${API_BASE}/api/v1/download/${type}/${taskId}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = `${type}_${taskId}.${type === 'output' ? 'webm' : 'mp4'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    function updateStatus(message, type) {
      const statusMessage = document.getElementById('statusMessage');
      statusMessage.textContent = message;
      statusMessage.className = `status ${type}`;
    }

    function updateProgress(percent) {
      const progressFill = document.getElementById('progressFill');
      progressFill.style.width = `${percent}%`;
      progressFill.textContent = `${percent}%`;
    }

    function showError(message) {
      const resultsContent = document.getElementById('resultsContent');
      resultsContent.innerHTML = `<div class="error">${message}</div>`;
      document.getElementById('results').style.display = 'block';
    }

    function resetForm() {
      document.getElementById('uploadBtn').disabled = false;
      currentTaskId = null;
      if (pollInterval) {
        clearInterval(pollInterval);
        pollInterval = null;
      }
    }

    // Check API health on page load
    window.addEventListener('load', async function () {
      try {
        const response = await fetch(`${API_BASE}/api/v1/health`);
        if (response.ok) {
          console.log('API is healthy');
        } else {
          showError('API is not responding. Make sure the server is running on localhost:8000');
        }
      } catch (error) {
        showError('Cannot connect to API. Make sure the server is running on localhost:8000');
      }
    });
  </script>
</body>

</html>