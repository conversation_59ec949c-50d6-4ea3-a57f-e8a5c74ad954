[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi[standard]>=0.116.1",
    "gitpython>=3.1.30",
    "matplotlib>=3.3",
    "numpy>=1.23.5",
    "opencv-python>=*********",
    "pandas>=1.1.4",
    "pillow>=10.3.0",
    "psutil>=7.0.0",
    "pyyaml>=5.3.1",
    "requests>=2.32.2",
    "scipy>=1.4.1",
    "seaborn>=0.11.0",
    "setuptools>=70.0.0",
    "thop>=0.1.1",
    "torch>=1.8.0",
    "torchvision>=0.9.0",
    "tqdm>=4.66.3",
    "twilio>=9.7.1",
    "ultralytics>=8.2.34",
]

[tool.uv.workspace]
members = [
    "api",
]
