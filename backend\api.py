from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import os
import shutil
import uuid
import asyncio
from datetime import datetime
import logging

from model import predictVideo
from location_generator import add_random_location
from notification import sendAlert

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Wildlife Anomaly Detection API",
    description="API for detecting wildlife anomalies in video using YOLOv5",
    version="1.0.0",
)

# Add CORS middleware to allow frontend requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this with your frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create necessary directories
os.makedirs("uploads", exist_ok=True)
os.makedirs("outputs", exist_ok=True)
os.makedirs("static/temp", exist_ok=True)
os.makedirs("static/out", exist_ok=True)

# Configuration
UPLOAD_DIR = "uploads"
OUTPUT_DIR = "outputs"
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
ALLOWED_VIDEO_EXTENSIONS = {".mp4", ".avi", ".mov", ".mkv", ".webm"}

# Default location for anomaly detection
DEFAULT_LAT = 22.606803
DEFAULT_LON = 85.338173
MAX_DISTANCE_KM = 20


# Pydantic models for request/response
class DetectionRequest(BaseModel):
    video_type: str  # "Daylight" or "Thermal"
    enable_alerts: bool = False
    location_lat: Optional[float] = DEFAULT_LAT
    location_lon: Optional[float] = DEFAULT_LON


class DetectionResult(BaseModel):
    frame_number: int
    detections: List[Dict[str, Any]]


class ProcessingResponse(BaseModel):
    task_id: str
    status: str
    message: str


class ResultResponse(BaseModel):
    task_id: str
    status: str
    original_video_url: Optional[str] = None
    processed_video_url: Optional[str] = None
    results: Optional[List[DetectionResult]] = None
    processing_time: Optional[float] = None
    error_message: Optional[str] = None


# In-memory storage for task status (use Redis in production)
task_storage: Dict[str, Dict] = {}


def validate_video_file(filename: str, file_size: int) -> bool:
    """Validate uploaded video file"""
    if file_size > MAX_FILE_SIZE:
        return False

    file_ext = os.path.splitext(filename)[1].lower()
    return file_ext in ALLOWED_VIDEO_EXTENSIONS


async def process_video_task(
    task_id: str,
    video_path: str,
    video_type: str,
    enable_alerts: bool,
    location_lat: float,
    location_lon: float,
):
    """Background task to process video"""
    try:
        task_storage[task_id]["status"] = "processing"
        task_storage[task_id]["message"] = "Processing video..."

        start_time = datetime.now()

        # Process video using the model
        logger.info(f"Starting video processing for task {task_id}")
        results, output_path = predictVideo(video_path, video_type)

        # Add location data to results
        updated_results = add_random_location(
            results, location_lat, location_lon, MAX_DISTANCE_KM
        )

        # Send alerts if enabled
        if enable_alerts and updated_results:
            try:
                sendAlert(updated_results)
            except Exception as e:
                logger.warning(f"Failed to send alerts for task {task_id}: {e}")

        processing_time = (datetime.now() - start_time).total_seconds()

        # Update task status
        task_storage[task_id].update(
            {
                "status": "completed",
                "message": "Video processed successfully",
                "results": updated_results,
                "processed_video_url": f"/api/v1/download/output/{task_id}",
                "processing_time": processing_time,
            }
        )

        logger.info(
            f"Video processing completed for task {task_id} in {processing_time:.2f}s"
        )

    except Exception as e:
        logger.error(f"Error processing video for task {task_id}: {e}")
        task_storage[task_id].update(
            {
                "status": "failed",
                "message": f"Processing failed: {str(e)}",
                "error_message": str(e),
            }
        )


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Wildlife Anomaly Detection API",
        "version": "1.0.0",
        "status": "active",
        "endpoints": {
            "upload": "/api/v1/upload",
            "status": "/api/v1/status/{task_id}",
            "download": "/api/v1/download/{type}/{task_id}",
            "health": "/api/v1/health",
        },
    }


@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.post("/api/v1/upload", response_model=ProcessingResponse)
async def upload_video(
    background_tasks: BackgroundTasks,
    video: UploadFile = File(...),
    video_type: str = "Daylight",  # Default to Daylight
    enable_alerts: bool = False,
    location_lat: float = DEFAULT_LAT,
    location_lon: float = DEFAULT_LON,
):
    """
    Upload video for wildlife anomaly detection

    - **video**: Video file (mp4, avi, mov, mkv, webm)
    - **video_type**: "Daylight" or "Thermal"
    - **enable_alerts**: Whether to send alerts for detections
    - **location_lat**: Latitude for location-based alerts
    - **location_lon**: Longitude for location-based alerts
    """
    try:
        # Validate video file
        if not validate_video_file(video.filename, video.size):
            raise HTTPException(
                status_code=400,
                detail="Invalid video file. File too large or unsupported format.",
            )

        # Validate video type
        if video_type not in ["Daylight", "Thermal"]:
            raise HTTPException(
                status_code=400,
                detail="video_type must be either 'Daylight' or 'Thermal'",
            )

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Save uploaded video
        file_extension = os.path.splitext(video.filename)[1]
        video_filename = f"{task_id}{file_extension}"
        video_path = os.path.join(UPLOAD_DIR, video_filename)

        with open(video_path, "wb") as buffer:
            shutil.copyfileobj(video.file, buffer)

        # Initialize task in storage
        task_storage[task_id] = {
            "status": "queued",
            "message": "Video uploaded, processing queued",
            "original_video_path": video_path,
            "original_video_url": f"/api/v1/download/input/{task_id}",
            "video_type": video_type,
            "created_at": datetime.now().isoformat(),
            "filename": video.filename,
        }

        # Add background task for video processing
        background_tasks.add_task(
            process_video_task,
            task_id,
            f"temp/{video_filename}",  # Relative path for the model
            video_type,
            enable_alerts,
            location_lat,
            location_lon,
        )

        # Copy to static temp directory for model processing
        temp_path = f"static/temp/{video_filename}"
        shutil.copy2(video_path, temp_path)

        logger.info(f"Video upload successful for task {task_id}")

        return ProcessingResponse(
            task_id=task_id,
            status="queued",
            message="Video uploaded successfully. Processing started.",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading video: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@app.get("/api/v1/status/{task_id}", response_model=ResultResponse)
async def get_task_status(task_id: str):
    """Get processing status and results for a task"""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task_data = task_storage[task_id]

    return ResultResponse(
        task_id=task_id,
        status=task_data.get("status"),
        original_video_url=task_data.get("original_video_url"),
        processed_video_url=task_data.get("processed_video_url"),
        results=task_data.get("results"),
        processing_time=task_data.get("processing_time"),
        error_message=task_data.get("error_message"),
    )


@app.get("/api/v1/download/input/{task_id}")
async def download_input_video(task_id: str):
    """Download original uploaded video"""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task_data = task_storage[task_id]
    video_path = task_data.get("original_video_path")

    if not video_path or not os.path.exists(video_path):
        raise HTTPException(status_code=404, detail="Original video not found")

    filename = task_data.get("filename", f"input_{task_id}.mp4")

    return FileResponse(path=video_path, filename=filename, media_type="video/mp4")


@app.get("/api/v1/download/output/{task_id}")
async def download_output_video(task_id: str):
    """Download processed video with detections"""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task_data = task_storage[task_id]

    if task_data.get("status") != "completed":
        raise HTTPException(
            status_code=400, detail="Video processing not completed yet"
        )

    # The output video is in static/out/output_video.webm
    output_path = "static/out/output_video.webm"

    if not os.path.exists(output_path):
        raise HTTPException(status_code=404, detail="Processed video not found")

    return FileResponse(
        path=output_path, filename=f"processed_{task_id}.webm", media_type="video/webm"
    )


@app.get("/api/v1/tasks")
async def list_tasks():
    """List all tasks (for debugging)"""
    return {
        "tasks": [
            {
                "task_id": task_id,
                "status": data.get("status"),
                "created_at": data.get("created_at"),
                "filename": data.get("filename"),
            }
            for task_id, data in task_storage.items()
        ]
    }


@app.delete("/api/v1/tasks/{task_id}")
async def delete_task(task_id: str):
    """Delete a task and its associated files"""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task_data = task_storage[task_id]

    # Clean up files
    try:
        if "original_video_path" in task_data:
            video_path = task_data["original_video_path"]
            if os.path.exists(video_path):
                os.remove(video_path)

        # Clean up temp file
        file_extension = ".mp4"  # Default
        temp_path = f"static/temp/{task_id}{file_extension}"
        if os.path.exists(temp_path):
            os.remove(temp_path)

    except Exception as e:
        logger.warning(f"Error cleaning up files for task {task_id}: {e}")

    # Remove from storage
    del task_storage[task_id]

    return {"message": f"Task {task_id} deleted successfully"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
